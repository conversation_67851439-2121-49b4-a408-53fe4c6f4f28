using Microsoft.Build.Locator;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.MSBuild;
using System.Text.RegularExpressions;

namespace SlnAnalyzer
{
    class Program
    {
        static async Task Main(string[] args)
        {
            try
            {
                if (args.Length == 0)
                {
                    Console.WriteLine("使用方法: dotnet run -- <解决方案文件路径>");
                    Console.WriteLine("示例: dotnet run -- \"C:\\path\\to\\solution.sln\"");
                    return;
                }

                var slnFile = args[0];
                Console.WriteLine($"正在分析解决方案: {slnFile}");

                // 检查是否为Unity项目
                var isUnityProject = await IsUnityProject(slnFile);
                if (isUnityProject)
                {
                    Console.WriteLine("检测到Unity项目，使用Unity兼容模式分析...");
                    await AnalyzeUnityProject(slnFile);
                    return;
                }

                // 注册MSBuild路径
                var msbuildDir = FindMSBuildPath();
                if (msbuildDir != null)
                {
                    MSBuildLocator.RegisterMSBuildPath(msbuildDir);
                    Console.WriteLine($"已注册MSBuild路径: {msbuildDir}");
                }
                else
                {
                    Console.WriteLine("警告: 无法找到MSBuild路径，尝试使用默认路径");
                    MSBuildLocator.RegisterDefaults();
                }

                // 创建MSBuild工作空间
                using var workspace = MSBuildWorkspace.Create();

                // 配置工作空间属性以更好地处理Unity项目
                workspace.WorkspaceFailed += (sender, e) =>
                {
                    Console.WriteLine($"工作空间警告: {e.Diagnostic.Kind} - {e.Diagnostic.Message}");
                };

                // 打开解决方案
                Console.WriteLine("正在打开解决方案...");
                var solution = await workspace.OpenSolutionAsync(slnFile);

                Console.WriteLine($"解决方案已打开，包含 {solution.Projects.Count()} 个项目");

                // 检查工作空间状态
                if (workspace.Diagnostics.Any())
                {
                    Console.WriteLine("工作空间诊断信息:");
                    foreach (var diagnostic in workspace.Diagnostics)
                    {
                        Console.WriteLine($"  - {diagnostic.Kind}: {diagnostic.Message}");
                    }
                }

                // 分析解决方案
                AnalyzeSolutionStructure(solution);

                // 分析项目
                foreach (var project in solution.Projects)
                {
                    AnalyzeProject(project);
                }

                Console.WriteLine("分析完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex}");
            }
        }
        
        static async Task<bool> IsUnityProject(string slnFile)
        {
            try
            {
                var slnContent = await File.ReadAllTextAsync(slnFile);

                // 检查解决方案文件中是否包含Unity特有的项目类型GUID
                var unityProjectTypeGuids = new[]
                {
                    "{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1}", // Unity项目类型
                    "Assembly-CSharp", // Unity生成的程序集名称
                    "Unity.", // Unity包项目前缀
                };

                return unityProjectTypeGuids.Any(guid => slnContent.Contains(guid, StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }

        static async Task AnalyzeUnityProject(string slnFile)
        {
            try
            {
                Console.WriteLine("\n=== Unity项目分析 ===");

                // 读取解决方案文件
                var slnContent = await File.ReadAllTextAsync(slnFile);
                var slnDir = Path.GetDirectoryName(slnFile) ?? "";

                // 解析项目文件路径
                var projectPaths = ExtractProjectPaths(slnContent, slnDir);

                Console.WriteLine($"找到 {projectPaths.Count} 个项目文件:");

                foreach (var projectPath in projectPaths)
                {
                    await AnalyzeUnityProjectFile(projectPath);
                }

                // 分析Unity特有的目录结构
                AnalyzeUnityDirectoryStructure(slnDir);

                Console.WriteLine("Unity项目分析完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unity项目分析失败: {ex.Message}");
            }
        }

        static List<string> ExtractProjectPaths(string slnContent, string slnDir)
        {
            var projectPaths = new List<string>();

            // 使用正则表达式提取项目路径
            var projectRegex = new Regex(@"Project\(""\{[^}]+\}""\)\s*=\s*""([^""]+)"",\s*""([^""]+)""", RegexOptions.IgnoreCase);
            var matches = projectRegex.Matches(slnContent);

            foreach (Match match in matches)
            {
                if (match.Groups.Count >= 3)
                {
                    var projectFile = match.Groups[2].Value;
                    if (projectFile.EndsWith(".csproj", StringComparison.OrdinalIgnoreCase))
                    {
                        var fullPath = Path.Combine(slnDir, projectFile);
                        if (File.Exists(fullPath))
                        {
                            projectPaths.Add(fullPath);
                        }
                    }
                }
            }

            return projectPaths;
        }

        static string? FindMSBuildPath()
        {
            try
            {
                // 尝试使用vswhere查找Visual Studio安装路径
                var vswherePath = @"C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe";
                if (File.Exists(vswherePath))
                {
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = vswherePath,
                        Arguments = "-latest -products * -requires Microsoft.Component.MSBuild -property installationPath",
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process != null)
                    {
                        var output = process.StandardOutput.ReadToEnd();
                        process.WaitForExit();

                        if (!string.IsNullOrEmpty(output))
                        {
                            var vsPath = output.Trim();
                            var msbuildPath = System.IO.Path.Combine(vsPath, "MSBuild", "Current", "Bin", "MSBuild.exe");
                            if (File.Exists(msbuildPath))
                            {
                                var directory = System.IO.Path.GetDirectoryName(msbuildPath);
                                return directory;
                            }
                        }
                    }
                }

                // 尝试常见的MSBuild路径
                var commonPaths = new[]
                {
                    @"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin",
                    @"C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin",
                    @"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin",
                    @"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin",
                    @"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin",
                    @"C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin"
                };

                foreach (var path in commonPaths)
                {
                    if (Directory.Exists(path))
                    {
                        return path;
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }
        
        static async Task AnalyzeUnityProjectFile(string projectPath)
        {
            try
            {
                Console.WriteLine($"\n--- 分析项目: {Path.GetFileNameWithoutExtension(projectPath)} ---");
                Console.WriteLine($"项目文件: {projectPath}");

                var projectContent = await File.ReadAllTextAsync(projectPath);

                // 提取基本信息
                var targetFramework = ExtractXmlValue(projectContent, "TargetFrameworkVersion");
                var assemblyName = ExtractXmlValue(projectContent, "AssemblyName");
                var outputType = ExtractXmlValue(projectContent, "OutputType");
                var langVersion = ExtractXmlValue(projectContent, "LangVersion");

                Console.WriteLine($"目标框架: {targetFramework ?? "未指定"}");
                Console.WriteLine($"程序集名称: {assemblyName ?? "未指定"}");
                Console.WriteLine($"输出类型: {outputType ?? "未指定"}");
                Console.WriteLine($"C#语言版本: {langVersion ?? "未指定"}");

                // 分析源文件
                var sourceFiles = ExtractSourceFiles(projectContent);
                if (sourceFiles.Any())
                {
                    Console.WriteLine($"源文件 ({sourceFiles.Count} 个):");
                    foreach (var file in sourceFiles.Take(10)) // 只显示前10个
                    {
                        Console.WriteLine($"  - {file}");
                    }
                    if (sourceFiles.Count > 10)
                    {
                        Console.WriteLine($"  ... 还有 {sourceFiles.Count - 10} 个文件");
                    }
                }

                // 分析引用
                var references = ExtractReferences(projectContent);
                if (references.Any())
                {
                    Console.WriteLine($"程序集引用 ({references.Count} 个):");
                    foreach (var reference in references.Take(10)) // 只显示前10个
                    {
                        Console.WriteLine($"  - {reference}");
                    }
                    if (references.Count > 10)
                    {
                        Console.WriteLine($"  ... 还有 {references.Count - 10} 个引用");
                    }
                }

                // 分析Unity特有的定义常量
                var defineConstants = ExtractXmlValue(projectContent, "DefineConstants");
                if (!string.IsNullOrEmpty(defineConstants))
                {
                    var unityDefines = defineConstants.Split(';')
                        .Where(d => d.StartsWith("UNITY_", StringComparison.OrdinalIgnoreCase))
                        .ToList();

                    if (unityDefines.Any())
                    {
                        Console.WriteLine($"Unity定义常量 ({unityDefines.Count} 个):");
                        foreach (var define in unityDefines.Take(5)) // 只显示前5个
                        {
                            Console.WriteLine($"  - {define}");
                        }
                        if (unityDefines.Count > 5)
                        {
                            Console.WriteLine($"  ... 还有 {unityDefines.Count - 5} 个定义");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析项目文件失败: {ex.Message}");
            }
        }

        static void AnalyzeUnityDirectoryStructure(string projectDir)
        {
            Console.WriteLine("\n=== Unity目录结构分析 ===");

            var unityDirs = new[]
            {
                "Assets", "Library", "ProjectSettings", "Packages", "Logs", "Temp", "UserSettings"
            };

            foreach (var dir in unityDirs)
            {
                var fullPath = Path.Combine(projectDir, dir);
                if (Directory.Exists(fullPath))
                {
                    var fileCount = Directory.GetFiles(fullPath, "*", SearchOption.AllDirectories).Length;
                    var dirCount = Directory.GetDirectories(fullPath, "*", SearchOption.AllDirectories).Length;
                    Console.WriteLine($"  {dir}/: {fileCount} 个文件, {dirCount} 个子目录");
                }
                else
                {
                    Console.WriteLine($"  {dir}/: 不存在");
                }
            }

            // 检查Unity版本
            var projectVersionPath = Path.Combine(projectDir, "ProjectSettings", "ProjectVersion.txt");
            if (File.Exists(projectVersionPath))
            {
                try
                {
                    var versionContent = File.ReadAllText(projectVersionPath);
                    var versionMatch = Regex.Match(versionContent, @"m_EditorVersion:\s*(.+)");
                    if (versionMatch.Success)
                    {
                        Console.WriteLine($"Unity版本: {versionMatch.Groups[1].Value.Trim()}");
                    }
                }
                catch
                {
                    Console.WriteLine("Unity版本: 无法读取");
                }
            }
        }

        static void AnalyzeSolutionStructure(Solution solution)
        {
            Console.WriteLine("\n=== 解决方案结构分析 ===");
            Console.WriteLine($"解决方案ID: {solution.Id}");
            Console.WriteLine($"解决方案版本: {solution.Version}");
            Console.WriteLine($"解决方案文件路径: {solution.FilePath}");

            // 按项目类型分组
            var projectGroups = solution.Projects
                .GroupBy(p => p.Language)
                .ToDictionary(g => g.Key, g => g.ToList());

            foreach (var group in projectGroups)
            {
                Console.WriteLine($"\n{group.Key} 项目 ({group.Value.Count} 个):");
                foreach (var project in group.Value)
                {
                    Console.WriteLine($"  - {project.Name} ({project.FilePath})");
                }
            }
        }
        
        static void AnalyzeProject(Project project)
        {
            Console.WriteLine($"\n=== 分析项目: {project.Name} ===");
            Console.WriteLine($"项目文件: {project.FilePath}");
            Console.WriteLine($"语言: {project.Language}");
            Console.WriteLine($"输出类型: {project.OutputFilePath}");

            // 分析项目引用
            var projectReferences = project.ProjectReferences;
            if (projectReferences.Any())
            {
                Console.WriteLine($"项目引用 ({projectReferences.Count()} 个):");
                foreach (var reference in projectReferences)
                {
                    var referencedProject = project.Solution.GetProject(reference.ProjectId);
                    if (referencedProject != null)
                    {
                        Console.WriteLine($"  - {referencedProject.Name}");
                    }
                }
            }

            // 分析程序集引用
            var metadataReferences = project.MetadataReferences;
            if (metadataReferences.Any())
            {
                Console.WriteLine($"程序集引用 ({metadataReferences.Count()} 个):");
                foreach (var reference in metadataReferences)
                {
                    if (reference is PortableExecutableReference peRef)
                    {
                        var filePath = peRef.FilePath;
                        if (!string.IsNullOrEmpty(filePath))
                        {
                            var fileName = filePath.Split('\\', '/').Last();
                            Console.WriteLine($"  - {fileName}");
                        }
                    }
                }
            }

            // 分析文档
            var documents = project.Documents;
            if (documents.Any())
            {
                Console.WriteLine($"文档文件 ({documents.Count()} 个):");
                foreach (var doc in documents)
                {
                    Console.WriteLine($"  - {doc.Name} ({doc.FilePath})");
                }
            }

            // 分析编译选项
            var compilationOptions = project.CompilationOptions;
            if (compilationOptions != null)
            {
                Console.WriteLine($"编译选项:");
                Console.WriteLine($"  - 输出类型: {compilationOptions.OutputKind}");
                Console.WriteLine($"  - 优化级别: {compilationOptions.OptimizationLevel}");
                Console.WriteLine($"  - 平台: {compilationOptions.Platform}");
            }
        }

        // 辅助方法：从XML内容中提取指定标签的值
        static string? ExtractXmlValue(string xmlContent, string tagName)
        {
            var pattern = $@"<{tagName}[^>]*>([^<]*)</{tagName}>";
            var match = Regex.Match(xmlContent, pattern, RegexOptions.IgnoreCase);
            return match.Success ? match.Groups[1].Value.Trim() : null;
        }

        // 辅助方法：提取源文件列表
        static List<string> ExtractSourceFiles(string projectContent)
        {
            var sourceFiles = new List<string>();
            var compilePattern = @"<Compile\s+Include=""([^""]+)""";
            var matches = Regex.Matches(projectContent, compilePattern, RegexOptions.IgnoreCase);

            foreach (Match match in matches)
            {
                if (match.Groups.Count > 1)
                {
                    sourceFiles.Add(match.Groups[1].Value);
                }
            }

            return sourceFiles;
        }

        // 辅助方法：提取程序集引用
        static List<string> ExtractReferences(string projectContent)
        {
            var references = new List<string>();
            var referencePattern = @"<Reference\s+Include=""([^""]+)""";
            var matches = Regex.Matches(projectContent, referencePattern, RegexOptions.IgnoreCase);

            foreach (Match match in matches)
            {
                if (match.Groups.Count > 1)
                {
                    references.Add(match.Groups[1].Value);
                }
            }

            return references;
        }
    }
}
