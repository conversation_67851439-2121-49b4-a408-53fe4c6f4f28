using Microsoft.Build.Locator;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.MSBuild;

namespace SlnAnalyzer
{
    class Program
    {
        static async Task Main(string[] args)
        {
            try
            {
                if (args.Length == 0)
                {
                    Console.WriteLine("使用方法: dotnet run -- <解决方案文件路径>");
                    Console.WriteLine("示例: dotnet run -- \"C:\\path\\to\\solution.sln\"");
                    return;
                }

                var slnFile = args[0];
                Console.WriteLine($"正在分析解决方案: {slnFile}");
                
                // 注册MSBuild路径
                var msbuildDir = FindMSBuildPath();
                if (msbuildDir != null)
                {
                    MSBuildLocator.RegisterMSBuildPath(msbuildDir);
                    Console.WriteLine($"已注册MSBuild路径: {msbuildDir}");
                }
                else
                {
                    Console.WriteLine("警告: 无法找到MSBuild路径，尝试使用默认路径");
                    MSBuildLocator.RegisterDefaults();
                }
                
                // 创建MSBuild工作空间
                using var workspace = MSBuildWorkspace.Create();
                
                // 打开解决方案
                Console.WriteLine("正在打开解决方案...");
                var solution = await workspace.OpenSolutionAsync(slnFile);
                
                Console.WriteLine($"解决方案已打开，包含 {solution.Projects.Count()} 个项目");
                
                // 检查工作空间状态
                if (workspace.Diagnostics.Any())
                {
                    Console.WriteLine("工作空间诊断信息:");
                    foreach (var diagnostic in workspace.Diagnostics)
                    {
                        Console.WriteLine($"  - {diagnostic.Kind}: {diagnostic.Message}");
                    }
                }
                
                // 分析解决方案
                AnalyzeSolutionStructure(solution);
                
                // 分析项目
                foreach (var project in solution.Projects)
                {
                    AnalyzeProject(project);
                }
                
                Console.WriteLine("分析完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex}");
            }
        }
        
        static string? FindMSBuildPath()
        {
            try
            {
                // 尝试使用vswhere查找Visual Studio安装路径
                var vswherePath = @"C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe";
                if (File.Exists(vswherePath))
                {
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = vswherePath,
                        Arguments = "-latest -products * -requires Microsoft.Component.MSBuild -property installationPath",
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    };
                    
                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process != null)
                    {
                        var output = process.StandardOutput.ReadToEnd();
                        process.WaitForExit();
                        
                        if (!string.IsNullOrEmpty(output))
                        {
                            var vsPath = output.Trim();
                            var msbuildPath = System.IO.Path.Combine(vsPath, "MSBuild", "Current", "Bin", "MSBuild.exe");
                            if (File.Exists(msbuildPath))
                            {
                                var directory = System.IO.Path.GetDirectoryName(msbuildPath);
                                return directory;
                            }
                        }
                    }
                }
                
                // 尝试常见的MSBuild路径
                var commonPaths = new[]
                {
                    @"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin",
                    @"C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin",
                    @"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin",
                    @"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin",
                    @"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin",
                    @"C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin"
                };
                
                foreach (var path in commonPaths)
                {
                    if (Directory.Exists(path))
                    {
                        return path;
                    }
                }
                
                return null;
            }
            catch
            {
                return null;
            }
        }
        
        static void AnalyzeSolutionStructure(Solution solution)
        {
            Console.WriteLine("\n=== 解决方案结构分析 ===");
            Console.WriteLine($"解决方案ID: {solution.Id}");
            Console.WriteLine($"解决方案版本: {solution.Version}");
            Console.WriteLine($"解决方案文件路径: {solution.FilePath}");
            
            // 按项目类型分组
            var projectGroups = solution.Projects
                .GroupBy(p => p.Language)
                .ToDictionary(g => g.Key, g => g.ToList());
            
            foreach (var group in projectGroups)
            {
                Console.WriteLine($"\n{group.Key} 项目 ({group.Value.Count} 个):");
                foreach (var project in group.Value)
                {
                    Console.WriteLine($"  - {project.Name} ({project.FilePath})");
                }
            }
        }
        
        static void AnalyzeProject(Project project)
        {
            Console.WriteLine($"\n=== 分析项目: {project.Name} ===");
            Console.WriteLine($"项目文件: {project.FilePath}");
            Console.WriteLine($"语言: {project.Language}");
            Console.WriteLine($"输出类型: {project.OutputFilePath}");
            
            // 分析项目引用
            var projectReferences = project.ProjectReferences;
            if (projectReferences.Any())
            {
                Console.WriteLine($"项目引用 ({projectReferences.Count()} 个):");
                foreach (var reference in projectReferences)
                {
                    var referencedProject = project.Solution.GetProject(reference.ProjectId);
                    if (referencedProject != null)
                    {
                        Console.WriteLine($"  - {referencedProject.Name}");
                    }
                }
            }
            
            // 分析程序集引用
            var metadataReferences = project.MetadataReferences;
            if (metadataReferences.Any())
            {
                Console.WriteLine($"程序集引用 ({metadataReferences.Count()} 个):");
                foreach (var reference in metadataReferences)
                {
                    if (reference is PortableExecutableReference peRef)
                    {
                        var filePath = peRef.FilePath;
                        if (!string.IsNullOrEmpty(filePath))
                        {
                            var fileName = filePath.Split('\\', '/').Last();
                            Console.WriteLine($"  - {fileName}");
                        }
                    }
                }
            }
            
            // 分析文档
            var documents = project.Documents;
            if (documents.Any())
            {
                Console.WriteLine($"文档文件 ({documents.Count()} 个):");
                foreach (var doc in documents)
                {
                    Console.WriteLine($"  - {doc.Name} ({doc.FilePath})");
                }
            }
            
            // 分析编译选项
            var compilationOptions = project.CompilationOptions;
            if (compilationOptions != null)
            {
                Console.WriteLine($"编译选项:");
                Console.WriteLine($"  - 输出类型: {compilationOptions.OutputKind}");
                Console.WriteLine($"  - 优化级别: {compilationOptions.OptimizationLevel}");
                Console.WriteLine($"  - 平台: {compilationOptions.Platform}");
            }
        }
    }
}
