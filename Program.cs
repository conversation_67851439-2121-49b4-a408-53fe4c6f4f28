using Microsoft.Build.Locator;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.FindSymbols;
using Microsoft.CodeAnalysis.MSBuild;
using Microsoft.CodeAnalysis.Text;
using System.Text.RegularExpressions;

namespace SlnAnalyzer
{
    class Program
    {
        static async Task Main(string[] args)
        {
            try
            {
                if (args.Length == 0)
                {
                    Console.WriteLine("使用方法:");
                    Console.WriteLine("  dotnet run -- <解决方案文件路径> [--analyze-symbol <文件路径> <行号> <列号>]");
                    Console.WriteLine("  dotnet run -- find-references <符号名称> [项目目录路径]");
                    Console.WriteLine("示例:");
                    Console.WriteLine("  dotnet run -- \"C:\\path\\to\\solution.sln\"");
                    Console.WriteLine("  dotnet run -- \"C:\\path\\to\\solution.sln\" --analyze-symbol \"Assets\\classC.cs\" 11 17");
                    Console.WriteLine("  dotnet run -- find-references Log");
                    Console.WriteLine("  dotnet run -- find-references Log \"C:\\path\\to\\unity\\project\"");
                    return;
                }

                // 检查是否为查找引用模式
                if (args[0] == "find-references" && args.Length >= 2)
                {
                    var symbol = args[1];
                    var projectDir = args.Length >= 3 ? args[2] : Directory.GetCurrentDirectory();

                    // 如果指定了项目路径，检查是否存在
                    if (!Directory.Exists(projectDir))
                    {
                        Console.WriteLine($"错误: 指定的项目目录不存在: {projectDir}");
                        return;
                    }

                    Console.WriteLine($"在目录中搜索符号 '{symbol}': {projectDir}");
                    await FindSymbolReferences(projectDir, symbol);
                    return;
                }

                var slnFile = args[0];
                Console.WriteLine($"正在分析解决方案: {slnFile}");

                // 检查是否为符号分析模式
                bool isSymbolAnalysis = args.Length >= 5 && args[1] == "--analyze-symbol";
                string? targetFile = null;
                int targetLine = 0, targetColumn = 0;

                if (isSymbolAnalysis)
                {
                    targetFile = args[2];
                    if (!int.TryParse(args[3], out targetLine) || !int.TryParse(args[4], out targetColumn))
                    {
                        Console.WriteLine("错误: 行号和列号必须是数字");
                        return;
                    }
                    Console.WriteLine($"符号分析模式: {targetFile} 第{targetLine}行第{targetColumn}列");
                }

                // 检查是否为Unity项目
                var isUnityProject = await IsUnityProject(slnFile);
                if (isUnityProject)
                {
                    Console.WriteLine("检测到Unity项目，使用Unity兼容模式分析...");
                    if (isSymbolAnalysis)
                    {
                        await AnalyzeUnitySymbol(slnFile, targetFile!, targetLine, targetColumn);
                    }
                    else
                    {
                        await AnalyzeUnityProject(slnFile);
                    }
                    return;
                }

                // 注册MSBuild路径
                var msbuildDir = FindMSBuildPath();
                if (msbuildDir != null)
                {
                    MSBuildLocator.RegisterMSBuildPath(msbuildDir);
                    Console.WriteLine($"已注册MSBuild路径: {msbuildDir}");
                }
                else
                {
                    Console.WriteLine("警告: 无法找到MSBuild路径，尝试使用默认路径");
                    MSBuildLocator.RegisterDefaults();
                }

                // 创建MSBuild工作空间
                using var workspace = MSBuildWorkspace.Create();

                // 配置工作空间属性以更好地处理Unity项目
                workspace.WorkspaceFailed += (sender, e) =>
                {
                    Console.WriteLine($"工作空间警告: {e.Diagnostic.Kind} - {e.Diagnostic.Message}");
                };

                // 打开解决方案
                Console.WriteLine("正在打开解决方案...");
                var solution = await workspace.OpenSolutionAsync(slnFile);

                Console.WriteLine($"解决方案已打开，包含 {solution.Projects.Count()} 个项目");

                // 检查工作空间状态
                if (workspace.Diagnostics.Any())
                {
                    Console.WriteLine("工作空间诊断信息:");
                    foreach (var diagnostic in workspace.Diagnostics)
                    {
                        Console.WriteLine($"  - {diagnostic.Kind}: {diagnostic.Message}");
                    }
                }

                // 分析解决方案
                AnalyzeSolutionStructure(solution);

                // 分析项目
                foreach (var project in solution.Projects)
                {
                    AnalyzeProject(project);
                }

                Console.WriteLine("分析完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex}");
            }
        }
        
        static async Task<bool> IsUnityProject(string slnFile)
        {
            try
            {
                var slnContent = await File.ReadAllTextAsync(slnFile);

                // 检查解决方案文件中是否包含Unity特有的项目类型GUID
                var unityProjectTypeGuids = new[]
                {
                    "{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1}", // Unity项目类型
                    "Assembly-CSharp", // Unity生成的程序集名称
                    "Unity.", // Unity包项目前缀
                };

                return unityProjectTypeGuids.Any(guid => slnContent.Contains(guid, StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }

        static async Task AnalyzeUnitySymbol(string slnFile, string targetFile, int targetLine, int targetColumn)
        {
            try
            {
                Console.WriteLine("\n=== Unity符号分析 ===");

                var slnDir = Path.GetDirectoryName(slnFile) ?? "";
                var fullTargetPath = Path.Combine(slnDir, targetFile);

                if (!File.Exists(fullTargetPath))
                {
                    Console.WriteLine($"错误: 找不到文件 {fullTargetPath}");
                    return;
                }

                // 读取目标文件
                var lines = await File.ReadAllLinesAsync(fullTargetPath);
                if (targetLine > lines.Length || targetLine < 1)
                {
                    Console.WriteLine($"错误: 行号 {targetLine} 超出文件范围 (1-{lines.Length})");
                    return;
                }

                var targetLineContent = lines[targetLine - 1];
                if (targetColumn > targetLineContent.Length || targetColumn < 1)
                {
                    Console.WriteLine($"错误: 列号 {targetColumn} 超出行范围 (1-{targetLineContent.Length})");
                    return;
                }

                Console.WriteLine($"目标位置: {targetFile}:{targetLine}:{targetColumn}");
                Console.WriteLine($"目标行内容: {targetLineContent}");
                Console.WriteLine($"目标列字符: '{targetLineContent[targetColumn - 1]}'");

                // 分析符号
                var symbol = ExtractSymbolAtPosition(targetLineContent, targetColumn);
                Console.WriteLine($"识别的符号: {symbol}");

                if (!string.IsNullOrEmpty(symbol))
                {
                    // 在所有源文件中查找引用
                    await FindSymbolReferences(slnDir, symbol);
                }

                Console.WriteLine("Unity符号分析完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unity符号分析失败: {ex.Message}");
            }
        }

        static async Task AnalyzeUnityProject(string slnFile)
        {
            try
            {
                Console.WriteLine("\n=== Unity项目分析 ===");

                // 读取解决方案文件
                var slnContent = await File.ReadAllTextAsync(slnFile);
                var slnDir = Path.GetDirectoryName(slnFile) ?? "";

                // 解析项目文件路径
                var projectPaths = ExtractProjectPaths(slnContent, slnDir);

                Console.WriteLine($"找到 {projectPaths.Count} 个项目文件:");

                foreach (var projectPath in projectPaths)
                {
                    await AnalyzeUnityProjectFile(projectPath);
                }

                // 分析Unity特有的目录结构
                AnalyzeUnityDirectoryStructure(slnDir);

                Console.WriteLine("Unity项目分析完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unity项目分析失败: {ex.Message}");
            }
        }

        static List<string> ExtractProjectPaths(string slnContent, string slnDir)
        {
            var projectPaths = new List<string>();

            // 使用正则表达式提取项目路径
            var projectRegex = new Regex(@"Project\(""\{[^}]+\}""\)\s*=\s*""([^""]+)"",\s*""([^""]+)""", RegexOptions.IgnoreCase);
            var matches = projectRegex.Matches(slnContent);

            foreach (Match match in matches)
            {
                if (match.Groups.Count >= 3)
                {
                    var projectFile = match.Groups[2].Value;
                    if (projectFile.EndsWith(".csproj", StringComparison.OrdinalIgnoreCase))
                    {
                        var fullPath = Path.Combine(slnDir, projectFile);
                        if (File.Exists(fullPath))
                        {
                            projectPaths.Add(fullPath);
                        }
                    }
                }
            }

            return projectPaths;
        }

        static string? FindMSBuildPath()
        {
            try
            {
                // 尝试使用vswhere查找Visual Studio安装路径
                var vswherePath = @"C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe";
                if (File.Exists(vswherePath))
                {
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = vswherePath,
                        Arguments = "-latest -products * -requires Microsoft.Component.MSBuild -property installationPath",
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process != null)
                    {
                        var output = process.StandardOutput.ReadToEnd();
                        process.WaitForExit();

                        if (!string.IsNullOrEmpty(output))
                        {
                            var vsPath = output.Trim();
                            var msbuildPath = System.IO.Path.Combine(vsPath, "MSBuild", "Current", "Bin", "MSBuild.exe");
                            if (File.Exists(msbuildPath))
                            {
                                var directory = System.IO.Path.GetDirectoryName(msbuildPath);
                                return directory;
                            }
                        }
                    }
                }

                // 尝试常见的MSBuild路径
                var commonPaths = new[]
                {
                    @"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin",
                    @"C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin",
                    @"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin",
                    @"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin",
                    @"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin",
                    @"C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin"
                };

                foreach (var path in commonPaths)
                {
                    if (Directory.Exists(path))
                    {
                        return path;
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }
        
        static async Task AnalyzeUnityProjectFile(string projectPath)
        {
            try
            {
                Console.WriteLine($"\n--- 分析项目: {Path.GetFileNameWithoutExtension(projectPath)} ---");
                Console.WriteLine($"项目文件: {projectPath}");

                var projectContent = await File.ReadAllTextAsync(projectPath);

                // 提取基本信息
                var targetFramework = ExtractXmlValue(projectContent, "TargetFrameworkVersion");
                var assemblyName = ExtractXmlValue(projectContent, "AssemblyName");
                var outputType = ExtractXmlValue(projectContent, "OutputType");
                var langVersion = ExtractXmlValue(projectContent, "LangVersion");

                Console.WriteLine($"目标框架: {targetFramework ?? "未指定"}");
                Console.WriteLine($"程序集名称: {assemblyName ?? "未指定"}");
                Console.WriteLine($"输出类型: {outputType ?? "未指定"}");
                Console.WriteLine($"C#语言版本: {langVersion ?? "未指定"}");

                // 分析源文件
                var sourceFiles = ExtractSourceFiles(projectContent);
                if (sourceFiles.Any())
                {
                    Console.WriteLine($"源文件 ({sourceFiles.Count} 个):");
                    foreach (var file in sourceFiles.Take(10)) // 只显示前10个
                    {
                        Console.WriteLine($"  - {file}");
                    }
                    if (sourceFiles.Count > 10)
                    {
                        Console.WriteLine($"  ... 还有 {sourceFiles.Count - 10} 个文件");
                    }
                }

                // 分析引用
                var references = ExtractReferences(projectContent);
                if (references.Any())
                {
                    Console.WriteLine($"程序集引用 ({references.Count} 个):");
                    foreach (var reference in references.Take(10)) // 只显示前10个
                    {
                        Console.WriteLine($"  - {reference}");
                    }
                    if (references.Count > 10)
                    {
                        Console.WriteLine($"  ... 还有 {references.Count - 10} 个引用");
                    }
                }

                // 分析Unity特有的定义常量
                var defineConstants = ExtractXmlValue(projectContent, "DefineConstants");
                if (!string.IsNullOrEmpty(defineConstants))
                {
                    var unityDefines = defineConstants.Split(';')
                        .Where(d => d.StartsWith("UNITY_", StringComparison.OrdinalIgnoreCase))
                        .ToList();

                    if (unityDefines.Any())
                    {
                        Console.WriteLine($"Unity定义常量 ({unityDefines.Count} 个):");
                        foreach (var define in unityDefines.Take(5)) // 只显示前5个
                        {
                            Console.WriteLine($"  - {define}");
                        }
                        if (unityDefines.Count > 5)
                        {
                            Console.WriteLine($"  ... 还有 {unityDefines.Count - 5} 个定义");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"分析项目文件失败: {ex.Message}");
            }
        }

        static void AnalyzeUnityDirectoryStructure(string projectDir)
        {
            Console.WriteLine("\n=== Unity目录结构分析 ===");

            var unityDirs = new[]
            {
                "Assets", "Library", "ProjectSettings", "Packages", "Logs", "Temp", "UserSettings"
            };

            foreach (var dir in unityDirs)
            {
                var fullPath = Path.Combine(projectDir, dir);
                if (Directory.Exists(fullPath))
                {
                    var fileCount = Directory.GetFiles(fullPath, "*", SearchOption.AllDirectories).Length;
                    var dirCount = Directory.GetDirectories(fullPath, "*", SearchOption.AllDirectories).Length;
                    Console.WriteLine($"  {dir}/: {fileCount} 个文件, {dirCount} 个子目录");
                }
                else
                {
                    Console.WriteLine($"  {dir}/: 不存在");
                }
            }

            // 检查Unity版本
            var projectVersionPath = Path.Combine(projectDir, "ProjectSettings", "ProjectVersion.txt");
            if (File.Exists(projectVersionPath))
            {
                try
                {
                    var versionContent = File.ReadAllText(projectVersionPath);
                    var versionMatch = Regex.Match(versionContent, @"m_EditorVersion:\s*(.+)");
                    if (versionMatch.Success)
                    {
                        Console.WriteLine($"Unity版本: {versionMatch.Groups[1].Value.Trim()}");
                    }
                }
                catch
                {
                    Console.WriteLine("Unity版本: 无法读取");
                }
            }
        }

        static void AnalyzeSolutionStructure(Solution solution)
        {
            Console.WriteLine("\n=== 解决方案结构分析 ===");
            Console.WriteLine($"解决方案ID: {solution.Id}");
            Console.WriteLine($"解决方案版本: {solution.Version}");
            Console.WriteLine($"解决方案文件路径: {solution.FilePath}");

            // 按项目类型分组
            var projectGroups = solution.Projects
                .GroupBy(p => p.Language)
                .ToDictionary(g => g.Key, g => g.ToList());

            foreach (var group in projectGroups)
            {
                Console.WriteLine($"\n{group.Key} 项目 ({group.Value.Count} 个):");
                foreach (var project in group.Value)
                {
                    Console.WriteLine($"  - {project.Name} ({project.FilePath})");
                }
            }
        }
        
        static void AnalyzeProject(Project project)
        {
            Console.WriteLine($"\n=== 分析项目: {project.Name} ===");
            Console.WriteLine($"项目文件: {project.FilePath}");
            Console.WriteLine($"语言: {project.Language}");
            Console.WriteLine($"输出类型: {project.OutputFilePath}");

            // 分析项目引用
            var projectReferences = project.ProjectReferences;
            if (projectReferences.Any())
            {
                Console.WriteLine($"项目引用 ({projectReferences.Count()} 个):");
                foreach (var reference in projectReferences)
                {
                    var referencedProject = project.Solution.GetProject(reference.ProjectId);
                    if (referencedProject != null)
                    {
                        Console.WriteLine($"  - {referencedProject.Name}");
                    }
                }
            }

            // 分析程序集引用
            var metadataReferences = project.MetadataReferences;
            if (metadataReferences.Any())
            {
                Console.WriteLine($"程序集引用 ({metadataReferences.Count()} 个):");
                foreach (var reference in metadataReferences)
                {
                    if (reference is PortableExecutableReference peRef)
                    {
                        var filePath = peRef.FilePath;
                        if (!string.IsNullOrEmpty(filePath))
                        {
                            var fileName = filePath.Split('\\', '/').Last();
                            Console.WriteLine($"  - {fileName}");
                        }
                    }
                }
            }

            // 分析文档
            var documents = project.Documents;
            if (documents.Any())
            {
                Console.WriteLine($"文档文件 ({documents.Count()} 个):");
                foreach (var doc in documents)
                {
                    Console.WriteLine($"  - {doc.Name} ({doc.FilePath})");
                }
            }

            // 分析编译选项
            var compilationOptions = project.CompilationOptions;
            if (compilationOptions != null)
            {
                Console.WriteLine($"编译选项:");
                Console.WriteLine($"  - 输出类型: {compilationOptions.OutputKind}");
                Console.WriteLine($"  - 优化级别: {compilationOptions.OptimizationLevel}");
                Console.WriteLine($"  - 平台: {compilationOptions.Platform}");
            }
        }

        // 辅助方法：从XML内容中提取指定标签的值
        static string? ExtractXmlValue(string xmlContent, string tagName)
        {
            var pattern = $@"<{tagName}[^>]*>([^<]*)</{tagName}>";
            var match = Regex.Match(xmlContent, pattern, RegexOptions.IgnoreCase);
            return match.Success ? match.Groups[1].Value.Trim() : null;
        }

        // 辅助方法：提取源文件列表
        static List<string> ExtractSourceFiles(string projectContent)
        {
            var sourceFiles = new List<string>();
            var compilePattern = @"<Compile\s+Include=""([^""]+)""";
            var matches = Regex.Matches(projectContent, compilePattern, RegexOptions.IgnoreCase);

            foreach (Match match in matches)
            {
                if (match.Groups.Count > 1)
                {
                    sourceFiles.Add(match.Groups[1].Value);
                }
            }

            return sourceFiles;
        }

        // 辅助方法：提取程序集引用
        static List<string> ExtractReferences(string projectContent)
        {
            var references = new List<string>();
            var referencePattern = @"<Reference\s+Include=""([^""]+)""";
            var matches = Regex.Matches(projectContent, referencePattern, RegexOptions.IgnoreCase);

            foreach (Match match in matches)
            {
                if (match.Groups.Count > 1)
                {
                    references.Add(match.Groups[1].Value);
                }
            }

            return references;
        }

        // 辅助方法：从指定位置提取符号
        static string ExtractSymbolAtPosition(string line, int column)
        {
            if (column < 1 || column > line.Length)
                return "";

            var charAtPosition = line[column - 1];

            // 如果是字符串字面量
            if (charAtPosition == '"' || (column > 1 && line[column - 2] == '"'))
            {
                var startQuote = line.LastIndexOf('"', column - 1);
                var endQuote = line.IndexOf('"', column);
                if (startQuote >= 0 && endQuote > startQuote)
                {
                    return line.Substring(startQuote, endQuote - startQuote + 1);
                }
            }

            // 如果是标识符（字母、数字、下划线）
            if (char.IsLetterOrDigit(charAtPosition) || charAtPosition == '_')
            {
                int start = column - 1;
                int end = column - 1;

                // 向前查找标识符开始
                while (start > 0 && (char.IsLetterOrDigit(line[start - 1]) || line[start - 1] == '_'))
                {
                    start--;
                }

                // 向后查找标识符结束
                while (end < line.Length - 1 && (char.IsLetterOrDigit(line[end + 1]) || line[end + 1] == '_'))
                {
                    end++;
                }

                return line.Substring(start, end - start + 1);
            }

            return charAtPosition.ToString();
        }

        // 使用 Roslyn API 查找符号引用
        static async Task FindSymbolReferences(string projectDir, string symbol)
        {
            Console.WriteLine($"\n=== 使用 Roslyn API 查找符号 '{symbol}' 的引用 ===");

            // 查找 Unity 解决方案文件
            var slnFiles = Directory.GetFiles(projectDir, "*.sln", SearchOption.TopDirectoryOnly);
            if (slnFiles.Length == 0)
            {
                Console.WriteLine("错误: 未找到解决方案文件，无法进行符号分析");
                Console.WriteLine("请确保在 Unity 项目根目录中运行，并且已生成解决方案文件");
                return;
            }

            var slnPath = slnFiles[0];
            Console.WriteLine($"找到解决方案文件: {slnPath}");

            try
            {
                // 确保 MSBuild 已注册
                if (!MSBuildLocator.IsRegistered)
                {
                    var msbuildDir = FindMSBuildPath();
                    if (msbuildDir != null)
                    {
                        MSBuildLocator.RegisterMSBuildPath(msbuildDir);
                        Console.WriteLine($"已注册MSBuild路径: {msbuildDir}");
                    }
                    else
                    {
                        MSBuildLocator.RegisterDefaults();
                        Console.WriteLine("使用默认MSBuild路径");
                    }
                }

                // 尝试使用 MSBuild 加载解决方案
                Console.WriteLine("正在尝试使用 MSBuild 加载解决方案...");
                var solution = await TryLoadSolutionWithMSBuild(slnPath);

                if (solution != null && solution.Projects.Any())
                {
                    Console.WriteLine($"MSBuild 加载成功，包含 {solution.Projects.Count()} 个项目");
                    await FindSymbolReferencesWithRoslyn(solution, symbol);
                }
                else
                {
                    Console.WriteLine("MSBuild 加载失败，尝试直接加载项目文件...");
                    await LoadProjectsDirectly(projectDir, symbol);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Roslyn 分析失败: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex}");
            }
        }

        // 尝试使用 MSBuild 加载解决方案
        static async Task<Solution?> TryLoadSolutionWithMSBuild(string slnPath)
        {
            try
            {
                // 创建 MSBuild 工作空间，配置 Unity 兼容属性
                var properties = new Dictionary<string, string>
                {
                    ["Configuration"] = "Debug",
                    ["Platform"] = "AnyCPU",
                    ["TargetFramework"] = "net471", // Unity 常用的框架版本
                    ["DefineConstants"] = "UNITY_EDITOR;UNITY_STANDALONE",
                    ["LangVersion"] = "latest"
                };

                using var workspace = MSBuildWorkspace.Create(properties);

                // 配置诊断处理
                var diagnostics = new List<WorkspaceDiagnostic>();
                workspace.WorkspaceFailed += (sender, e) =>
                {
                    diagnostics.Add(e.Diagnostic);
                };

                var solution = await workspace.OpenSolutionAsync(slnPath);

                // 只显示严重错误，忽略警告
                var errors = diagnostics.Where(d => d.Kind == WorkspaceDiagnosticKind.Failure).ToList();
                if (errors.Any())
                {
                    Console.WriteLine($"MSBuild 加载遇到 {errors.Count} 个错误（忽略警告）");
                    foreach (var error in errors.Take(5)) // 只显示前5个错误
                    {
                        Console.WriteLine($"  错误: {error.Message}");
                    }
                }

                return solution;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MSBuild 加载异常: {ex.Message}");
                return null;
            }
        }

        // 直接分析源代码文件（不依赖 MSBuild）
        static async Task LoadProjectsDirectly(string projectDir, string symbol)
        {
            Console.WriteLine("正在直接分析源代码文件...");

            try
            {
                // 查找所有 .cs 文件
                var assetsDir = Path.Combine(projectDir, "Assets");
                if (!Directory.Exists(assetsDir))
                {
                    Console.WriteLine("未找到 Assets 目录");
                    return;
                }

                var sourceFiles = Directory.GetFiles(assetsDir, "*.cs", SearchOption.AllDirectories);
                Console.WriteLine($"找到 {sourceFiles.Length} 个 C# 源文件");

                if (sourceFiles.Length == 0)
                {
                    Console.WriteLine("未找到 C# 源文件");
                    return;
                }

                var allReferences = new List<(string filePath, int line, int column, string text)>();
                var symbolsFound = new List<ISymbol>();

                // 创建内存中的工作空间
                using var workspace = new AdhocWorkspace();

                // 查找 Unity 引擎程序集
                var unityReferences = new List<MetadataReference>
                {
                    MetadataReference.CreateFromFile(typeof(object).Assembly.Location),
                    MetadataReference.CreateFromFile(typeof(System.Console).Assembly.Location),
                    MetadataReference.CreateFromFile(typeof(System.Linq.Enumerable).Assembly.Location)
                };

                // 尝试添加 Unity 引擎引用
                try
                {
                    // 查找 Unity 安装目录
                    var unityEditorPath = Environment.GetEnvironmentVariable("UNITY_EDITOR_PATH");
                    if (string.IsNullOrEmpty(unityEditorPath))
                    {
                        // 尝试常见的 Unity 安装路径
                        var commonPaths = new[]
                        {
                            @"C:\Program Files\Unity\Hub\Editor",
                            @"C:\Program Files\Unity\Editor",
                            @"C:\Program Files (x86)\Unity\Editor"
                        };

                        foreach (var basePath in commonPaths)
                        {
                            if (Directory.Exists(basePath))
                            {
                                var versions = Directory.GetDirectories(basePath);
                                if (versions.Length > 0)
                                {
                                    unityEditorPath = Path.Combine(versions[0], "Editor");
                                    break;
                                }
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(unityEditorPath) && Directory.Exists(unityEditorPath))
                    {
                        var unityEnginePath = Path.Combine(unityEditorPath, "Data", "Managed", "UnityEngine.dll");
                        if (File.Exists(unityEnginePath))
                        {
                            unityReferences.Add(MetadataReference.CreateFromFile(unityEnginePath));
                            Console.WriteLine($"添加了 Unity 引擎引用: {unityEnginePath}");
                        }

                        var unityEditorDllPath = Path.Combine(unityEditorPath, "Data", "Managed", "UnityEditor.dll");
                        if (File.Exists(unityEditorDllPath))
                        {
                            unityReferences.Add(MetadataReference.CreateFromFile(unityEditorDllPath));
                            Console.WriteLine($"添加了 Unity 编辑器引用: {unityEditorDllPath}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("未找到 Unity 安装目录，将无法解析 Unity API 引用");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"添加 Unity 引用时出错: {ex.Message}");
                }

                // 创建项目
                var projectInfo = ProjectInfo.Create(
                    ProjectId.CreateNewId(),
                    VersionStamp.Create(),
                    "UnityProject",
                    "UnityProject",
                    LanguageNames.CSharp,
                    metadataReferences: unityReferences);

                var project = workspace.AddProject(projectInfo);

                // 添加所有源文件
                foreach (var sourceFile in sourceFiles)
                {
                    try
                    {
                        var sourceText = await File.ReadAllTextAsync(sourceFile);
                        var document = project.AddDocument(Path.GetFileName(sourceFile), SourceText.From(sourceText), filePath: sourceFile);
                        project = document.Project;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  无法读取文件 {sourceFile}: {ex.Message}");
                    }
                }

                Console.WriteLine($"成功加载 {project.Documents.Count()} 个文档到项目中");

                // 获取编译
                var compilation = await project.GetCompilationAsync();
                if (compilation != null)
                {
                    Console.WriteLine($"编译成功，包含 {compilation.SyntaxTrees.Count()} 个语法树");

                    // 查找符号
                    var symbols = compilation.GetSymbolsWithName(
                        name => name.Equals(symbol, StringComparison.OrdinalIgnoreCase),
                        SymbolFilter.All);

                    foreach (var sym in symbols)
                    {
                        symbolsFound.Add(sym);
                        Console.WriteLine($"  找到符号: {sym.ToDisplayString()} ({sym.Kind})");
                    }

                    // 查找引用
                    foreach (var sym in symbolsFound)
                    {
                        var references = await SymbolFinder.FindReferencesAsync(sym, workspace.CurrentSolution);

                        foreach (var reference in references)
                        {
                            foreach (var location in reference.Locations)
                            {
                                var sourceTree = location.Location.SourceTree;
                                if (sourceTree != null && !string.IsNullOrEmpty(sourceTree.FilePath))
                                {
                                    var lineSpan = sourceTree.GetLineSpan(location.Location.SourceSpan);
                                    var line = lineSpan.StartLinePosition.Line + 1;
                                    var column = lineSpan.StartLinePosition.Character + 1;

                                    var sourceText = await sourceTree.GetTextAsync();
                                    var lineText = sourceText.Lines[lineSpan.StartLinePosition.Line].ToString().Trim();

                                    allReferences.Add((sourceTree.FilePath, line, column, lineText));
                                }
                            }
                        }
                    }
                }
                else
                {
                    Console.WriteLine("编译失败");
                }

                // 输出结果
                Console.WriteLine($"\n=== 源代码分析结果 ===");
                Console.WriteLine($"符号: {symbol}");
                Console.WriteLine($"找到符号定义: {symbolsFound.Count} 个");
                Console.WriteLine($"总引用次数: {allReferences.Count}");
                Console.WriteLine($"引用文件数: {allReferences.Select(r => r.filePath).Distinct().Count()}");

                if (allReferences.Count > 0)
                {
                    Console.WriteLine("\n引用位置 (前50个):");
                    foreach (var (filePath, line, column, text) in allReferences.Take(50))
                    {
                        var relativePath = Path.GetRelativePath(projectDir, filePath);
                        Console.WriteLine($"  {relativePath}:{line}:{column} - {text}");
                    }

                    if (allReferences.Count > 50)
                    {
                        Console.WriteLine($"  ... 还有 {allReferences.Count - 50} 个引用");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"源代码分析失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }

        // 使用 Roslyn API 查找符号引用
        static async Task FindSymbolReferencesWithRoslyn(Solution solution, string symbolName)
        {
            Console.WriteLine($"使用 Roslyn API 搜索符号: {symbolName}");

            var allReferences = new List<(string filePath, int line, int column, string text)>();
            var symbolsFound = new List<ISymbol>();

            try
            {
                foreach (var project in solution.Projects)
                {
                    Console.WriteLine($"正在分析项目: {project.Name}");

                    var compilation = await project.GetCompilationAsync();
                    if (compilation == null)
                    {
                        Console.WriteLine($"  项目 {project.Name} 编译失败，跳过");
                        continue;
                    }

                    // 查找符号定义
                    var symbols = compilation.GetSymbolsWithName(
                        name => name.Equals(symbolName, StringComparison.OrdinalIgnoreCase),
                        SymbolFilter.All);

                    foreach (var symbol in symbols)
                    {
                        if (!symbolsFound.Any(s => s.Equals(symbol)))
                        {
                            symbolsFound.Add(symbol);
                            Console.WriteLine($"  找到符号: {symbol.ToDisplayString()} ({symbol.Kind})");
                        }
                    }
                }

                // 查找所有符号的引用
                foreach (var symbol in symbolsFound)
                {
                    Console.WriteLine($"\n查找符号 '{symbol.ToDisplayString()}' 的引用...");

                    var references = await Microsoft.CodeAnalysis.FindSymbols.SymbolFinder.FindReferencesAsync(
                        symbol, solution);

                    foreach (var reference in references)
                    {
                        foreach (var location in reference.Locations)
                        {
                            var sourceTree = location.Location.SourceTree;
                            if (sourceTree != null && !string.IsNullOrEmpty(sourceTree.FilePath))
                            {
                                var lineSpan = sourceTree.GetLineSpan(location.Location.SourceSpan);
                                var line = lineSpan.StartLinePosition.Line + 1;
                                var column = lineSpan.StartLinePosition.Character + 1;

                                // 获取引用的文本内容
                                var sourceText = await sourceTree.GetTextAsync();
                                var lineText = sourceText.Lines[lineSpan.StartLinePosition.Line].ToString().Trim();

                                allReferences.Add((sourceTree.FilePath, line, column, lineText));
                            }
                        }
                    }
                }

                // 输出结果
                Console.WriteLine($"\n=== Roslyn 符号引用统计 ===");
                Console.WriteLine($"符号: {symbolName}");
                Console.WriteLine($"找到符号定义: {symbolsFound.Count} 个");
                Console.WriteLine($"总引用次数: {allReferences.Count}");
                Console.WriteLine($"引用文件数: {allReferences.Select(r => r.filePath).Distinct().Count()}");

                if (allReferences.Any())
                {
                    Console.WriteLine("\n引用位置 (前50个):");
                    foreach (var (filePath, line, column, text) in allReferences.Take(50))
                    {
                        var relativePath = Path.GetRelativePath(Directory.GetCurrentDirectory(), filePath);
                        Console.WriteLine($"  {relativePath}:{line}:{column} - {text}");
                    }

                    if (allReferences.Count > 50)
                    {
                        Console.WriteLine($"  ... 还有 {allReferences.Count - 50} 个引用");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Roslyn 符号查找出错: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }
    }
}
