# 解决方案文件分析器 (SlnAnalyzer)

这是一个C#控制台应用程序，用于分析Visual Studio解决方案(.sln)文件，提供详细的解决方案结构和项目信息分析。

## 功能特性

- 自动查找并注册MSBuild路径
- 分析解决方案结构
- 分析项目引用关系
- 分析程序集引用
- 分析项目编译选项
- 支持命令行参数

## 系统要求

- .NET 6.0 或更高版本
- Visual Studio 2019/2022 或 MSBuild
- Windows 操作系统

## 安装和构建

1. 确保已安装.NET 6.0 SDK
2. 克隆或下载项目文件
3. 在项目目录中运行以下命令：

```bash
dotnet restore
dotnet build
```

## 使用方法

### 基本用法

```bash
dotnet run -- --sln "path/to/your/solution.sln"
```

### 指定MSBuild路径

```bash
dotnet run -- --sln "path/to/your/solution.sln" --msbuild "C:\path\to\msbuild\bin"
```

### 参数说明

- `--sln`: 要分析的解决方案文件路径（必需）
- `--msbuild`: MSBuild路径（可选，如果不指定将自动查找）

## 输出示例

程序会输出以下信息：

1. **解决方案结构分析**
   - 解决方案ID和版本
   - 项目数量和类型分组

2. **项目详细分析**
   - 项目基本信息
   - 项目引用关系
   - 程序集引用
   - 源代码文件
   - 编译选项

## 自动MSBuild路径查找

程序会自动尝试以下路径：

1. 使用`vswhere.exe`查找最新安装的Visual Studio
2. 常见Visual Studio安装路径
3. 如果都找不到，使用默认注册

## 依赖包

- `Microsoft.CodeAnalysis` - Roslyn编译器API
- `Microsoft.CodeAnalysis.CSharp.Workspaces` - C#工作空间支持
- `Microsoft.Build.Locator` - MSBuild定位器
- `System.CommandLine` - 命令行参数解析

## 注意事项

- 确保有足够的权限访问解决方案文件和项目文件
- 大型解决方案可能需要较长的加载时间
- 某些项目类型可能不完全支持

## 故障排除

如果遇到MSBuild路径问题：

1. 手动指定MSBuild路径：`--msbuild "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin"`
2. 确保已安装Visual Studio或Build Tools
3. 检查环境变量设置
