{"version": 2, "dgSpecHash": "RnrqpNHJVTM=", "success": true, "projectFilePath": "E:\\workspace\\msbuild\\faa\\SlnAnalyzer.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.framework\\16.10.0\\microsoft.build.framework.16.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.locator\\1.6.10\\microsoft.build.locator.1.6.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis\\4.8.0\\microsoft.codeanalysis.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.3.4\\microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.8.0\\microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.8.0\\microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp.workspaces\\4.8.0\\microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.visualbasic\\4.8.0\\microsoft.codeanalysis.visualbasic.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.visualbasic.workspaces\\4.8.0\\microsoft.codeanalysis.visualbasic.workspaces.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.common\\4.8.0\\microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.msbuild\\4.7.0\\microsoft.codeanalysis.workspaces.msbuild.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\4.7.0\\microsoft.win32.systemevents.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.commandline\\2.0.0-beta4.22272.1\\system.commandline.2.0.0-beta4.22272.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition\\7.0.0\\system.composition.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.attributedmodel\\7.0.0\\system.composition.attributedmodel.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.convention\\7.0.0\\system.composition.convention.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.hosting\\7.0.0\\system.composition.hosting.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.runtime\\7.0.0\\system.composition.runtime.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.typedparts\\7.0.0\\system.composition.typedparts.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\8.0.0\\system.configuration.configurationmanager.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\8.0.0\\system.diagnostics.eventlog.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\4.7.0\\system.drawing.common.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\7.0.0\\system.io.pipelines.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\8.0.0\\system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\4.7.0\\system.security.permissions.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.windows.extensions\\4.7.0\\system.windows.extensions.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\8.0.18\\microsoft.netcore.app.ref.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\8.0.18\\microsoft.windowsdesktop.app.ref.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\8.0.18\\microsoft.aspnetcore.app.ref.8.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.host.win-x64\\8.0.18\\microsoft.netcore.app.host.win-x64.8.0.18.nupkg.sha512"], "logs": [{"code": "NU1608", "level": "Warning", "message": "检测到的包版本在依赖项约束之外: Microsoft.CodeAnalysis.Workspaces.MSBuild 4.7.0 需要 Microsoft.CodeAnalysis.Common (= 4.7.0)，但版本 Microsoft.CodeAnalysis.Common 4.8.0 已解决。", "projectPath": "E:\\workspace\\msbuild\\faa\\SlnAnalyzer.csproj", "warningLevel": 1, "filePath": "E:\\workspace\\msbuild\\faa\\SlnAnalyzer.csproj", "libraryId": "Microsoft.CodeAnalysis.Common", "targetGraphs": ["net8.0"]}, {"code": "NU1608", "level": "Warning", "message": "检测到的包版本在依赖项约束之外: Microsoft.CodeAnalysis.Workspaces.MSBuild 4.7.0 需要 Microsoft.CodeAnalysis.Workspaces.Common (= 4.7.0)，但版本 Microsoft.CodeAnalysis.Workspaces.Common 4.8.0 已解决。", "projectPath": "E:\\workspace\\msbuild\\faa\\SlnAnalyzer.csproj", "warningLevel": 1, "filePath": "E:\\workspace\\msbuild\\faa\\SlnAnalyzer.csproj", "libraryId": "Microsoft.CodeAnalysis.Workspaces.Common", "targetGraphs": ["net8.0"]}]}